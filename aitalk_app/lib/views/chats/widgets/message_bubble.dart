import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../core/user/user_profile.dart';
import 'package:audioplayers/audioplayers.dart';

/// 单条聊天消息模型（公共群和私有群通用）
class ChatMessage {
  final int srcId; // 发送者 SrcID
  final String content; // 文本内容或占位
  final bool isMine; // 是否为自己发送
  final String time; // HH:mm 字符串
  final bool isVoice; // 是否为语音消息
  final String? voicePath; // 本地 WAV 路径
  final String? groupId; // 群组ID，用于判断是否为私有群
  final int? memberId; // 群内成员ID，用于私有群显示

  ChatMessage({
    required this.srcId,
    required this.content,
    required this.isMine,
    required this.time,
    this.isVoice = false,
    this.voicePath,
    this.groupId,
    this.memberId,
  });
}

/// 聊天气泡组件，可展示头像、用户名与消息内容
class MessageBubble extends StatefulWidget {
  const MessageBubble({super.key, required this.message});

  final ChatMessage message;

  @override
  State<MessageBubble> createState() => _MessageBubbleState();
}

class _MessageBubbleState extends State<MessageBubble>
    with SingleTickerProviderStateMixin {
  late final AudioPlayer _player;
  bool _isPlaying = false;
  Duration? _duration;

  late final AnimationController _waveController;

  @override
  void initState() {
    super.initState();
    _player = AudioPlayer();

    // 预先获取音频时长
    if (widget.message.isVoice && widget.message.voicePath != null) {
      _initVoice();
    }

    _waveController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    // 播放完成后重置状态
    _player.onPlayerComplete.listen((event) {
      _stopPlayback();
    });
  }

  Future<void> _initVoice() async {
    try {
      final voicePath = widget.message.voicePath!;
      debugPrint('🎵 初始化语音文件: $voicePath');

      await _player.setSource(DeviceFileSource(voicePath));
      final dur = await _player.getDuration();

      if (!mounted) return;

      if (dur != null && dur.inMilliseconds > 0) {
        setState(() {
          _duration = dur;
        });
        debugPrint('✅ 获取语音时长成功: ${dur.inSeconds}秒');
      } else {
        debugPrint('⚠️ 语音时长为空或无效，设置为0秒');
        setState(() {
          _duration = const Duration(seconds: 0);
        });
      }
    } catch (e) {
      debugPrint('❌ 获取语音时长失败: $e，设置为0秒');
      setState(() {
        _duration = const Duration(seconds: 0);
      });
    }
  }

  @override
  void dispose() {
    _player.dispose();
    _waveController.dispose();
    super.dispose();
  }

  // 根据消息归属生成用户名
  String _username() {
    if (widget.message.isMine) {
      final nick = UserProfile.instance.nicknameNotifier.value;
      if (nick != null && nick.trim().isNotEmpty) {
        return nick.trim();
      }
      return '我';
    }

    // 检查是否为私有群且有memberId信息
    if (widget.message.groupId != null &&
        widget.message.memberId != null &&
        !_isPublicGroup(widget.message.groupId!)) {
      // 私有群：使用memberId显示
      return '匿名用户 - ${widget.message.memberId}';
    }

    // 公共群或无群组信息：使用srcId的低8位显示
    final hex = widget.message.srcId
        .toRadixString(16)
        .toUpperCase()
        .padLeft(2, '0');
    return '匿名用户 - 0x$hex';
  }

  // 判断是否为公共群
  bool _isPublicGroup(String groupId) {
    // 公共群ID格式：10000001-10000010
    if (groupId.length == 8) {
      try {
        final id = int.parse(groupId, radix: 16);
        return id >= 0x10000001 && id <= 0x10000010;
      } catch (_) {
        return false;
      }
    }
    return false;
  }

  Color _avatarColor(BuildContext context) {
    if (widget.message.isMine) return context.brandPrimary;
    const palette = [
      Colors.indigo,
      Colors.green,
      Colors.deepOrange,
      Colors.teal,
      Colors.purple,
      Colors.brown,
      Colors.blueGrey,
      Colors.pink,
      Colors.cyan,
      Colors.amber,
    ];
    return palette[widget.message.srcId % palette.length];
  }

  // 计算头像文本
  String _avatarText(String username) {
    if (!widget.message.isMine && username.startsWith('匿名用户')) return '匿';
    return username.substring(0, 1);
  }

  /// 停止播放并重置
  Future<void> _stopPlayback() async {
    await _player.stop();
    await _player.seek(Duration.zero);
    if (mounted) {
      setState(() {
        _isPlaying = false;
      });
      _waveController.stop();
    }
  }

  /// 点击气泡切换播放状态
  Future<void> _togglePlay() async {
    if (!widget.message.isVoice || widget.message.voicePath == null) return;

    if (_isPlaying) {
      // 暂停并复位
      await _stopPlayback();
    } else {
      try {
        await _player.play(DeviceFileSource(widget.message.voicePath!));
        setState(() {
          _isPlaying = true;
        });
        _waveController.repeat(reverse: true);
      } catch (_) {
        // ignore play error
      }
    }
  }

  // 构建语音波形动画
  Widget _buildWaveform() {
    // 三根柱状条，使用 AnimationController 控制高度
    return AnimatedBuilder(
      animation: _waveController,
      builder: (context, child) {
        double v = _waveController.value; // 0~1
        // 通过相位差制造动态效果
        double h1 = 4 + (8 * v);
        double h2 = 4 + (8 * ((v + 0.33) % 1));
        double h3 = 4 + (8 * ((v + 0.66) % 1));
        Widget _bar(double h) => Container(
          width: 3,
          height: h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(1),
          ),
        );
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _bar(h1),
            const SizedBox(width: 2),
            _bar(h2),
            const SizedBox(width: 2),
            _bar(h3),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final bubbleColor = widget.message.isMine
        ? context.brandPrimary
        : Colors.green.shade800;
    final radius = widget.message.isMine
        ? const BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(0),
            bottomLeft: Radius.circular(12),
            bottomRight: Radius.circular(12),
          )
        : const BorderRadius.only(
            topLeft: Radius.circular(0),
            topRight: Radius.circular(12),
            bottomLeft: Radius.circular(12),
            bottomRight: Radius.circular(12),
          );

    // 头像 + 时间列
    Widget avatarColumn() {
      final username = _username();
      final avatarColor = _avatarColor(context);
      final avatarText = _avatarText(username);
      return Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: avatarColor,
              borderRadius: BorderRadius.circular(6),
            ),
            alignment: Alignment.center,
            child: Text(
              avatarText,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          if (widget.message.time.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              widget.message.time,
              style: TextStyle(fontSize: 10, color: context.textTertiaryCol),
            ),
          ],
        ],
      );
    }

    // 生成消息内容区域
    Widget messageContent() {
      if (widget.message.isVoice) {
        if (_isPlaying) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildWaveform(),
              if (_duration != null) ...[
                const SizedBox(width: 6),
                Text(
                  '${_duration!.inSeconds}"',
                  style: const TextStyle(color: Colors.white),
                ),
              ],
            ],
          );
        } else {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.graphic_eq, color: Colors.white),
              const SizedBox(width: 4),
              Text(
                '${(_duration?.inSeconds ?? 0)}"',
                style: const TextStyle(color: Colors.white),
              ),
            ],
          );
        }
      } else {
        // 文本消息
        return Text(
          widget.message.content,
          style: const TextStyle(color: Colors.white),
          softWrap: true, // 启用软换行
          overflow: TextOverflow.visible, // 允许文本可见，不截断
        );
      }
    }

    final bubble = Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.7, // 最大宽度为屏幕宽度的70%
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(color: bubbleColor, borderRadius: radius),
      child: messageContent(),
    );

    Widget bubbleWrapper = bubble;
    if (widget.message.isVoice && widget.message.voicePath != null) {
      bubbleWrapper = GestureDetector(onTap: _togglePlay, child: bubble);
    }

    // 气泡 + 用户名
    final bubbleColumn = Column(
      crossAxisAlignment: widget.message.isMine
          ? CrossAxisAlignment.end
          : CrossAxisAlignment.start,
      children: [
        Text(
          _username(),
          style: TextStyle(fontSize: 10, color: context.textTertiaryCol),
        ),
        const SizedBox(height: 2),
        bubbleWrapper,
      ],
    );

    final rowChildren = widget.message.isMine
        ? <Widget>[bubbleColumn, const SizedBox(width: 8), avatarColumn()]
        : <Widget>[avatarColumn(), const SizedBox(width: 8), bubbleColumn];

    return Row(
      mainAxisAlignment: widget.message.isMine
          ? MainAxisAlignment.end
          : MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: rowChildren,
    );
  }
}
